"use client"

import React, { useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { useAdminAuth } from "@/hooks/useAdminAuth"
import AdminProtectedRoute from "@/components/AdminProtectedRoute"
import {
  BarChart3,
  Users,
  Search,
  ClipboardCheck,
  LogOut,
  Menu,
  X,
} from "lucide-react"
import Image from "next/image"

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user, logout } = useAdminAuth()

  const navigation = [
    {
      name: "Dashboard",
      href: "/admin",
      icon: BarChart3,
      current: pathname === "/admin",
    },
    {
      name: "Buscar Usuários",
      href: "/admin/users",
      icon: Search,
      current: pathname === "/admin/users",
    },
    {
      name: "Aprovações Pendentes",
      href: "/admin/approvals",
      icon: ClipboardCheck,
      current: pathname === "/admin/approvals",
    },
    {
      name: "Todas as Solicitações",
      href: "/admin/requests",
      icon: Users,
      current: pathname === "/admin/requests",
    },
  ]

  return (
    <AdminProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div className="fixed inset-0 z-40 lg:hidden">
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
          </div>
        )}

        {/* Sidebar */}
        <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-sidebar shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
          <div className="flex flex-col h-full">
            {/* Logo */}
            <div className="flex items-center justify-between h-16 px-6 border-b border-sidebar-border">
              <div className="flex items-center">
                <Image src="/images/locpay-logo.png" alt="LocPay Admin" width={120} height={32} className="h-8 w-auto brightness-0 invert" />
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden text-sidebar-foreground hover:bg-sidebar-accent"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6 space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <Button
                    key={item.name}
                    variant={item.current ? "default" : "ghost"}
                    className={`w-full justify-start h-12 text-left transition-all duration-200 ${
                      item.current
                        ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-md"
                        : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                    }`}
                    onClick={() => router.push(item.href)}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Button>
                )
              })}
            </nav>

            {/* Admin user info */}
            <div className="border-t border-sidebar-border p-4">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-sidebar-primary rounded-full flex items-center justify-center">
                  <span className="text-sidebar-primary-foreground text-sm font-semibold">
                    {user?.name?.charAt(0) || 'A'}
                  </span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-sidebar-foreground">{user?.name}</p>
                  <p className="text-xs text-sidebar-foreground/70">Administrador</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={logout}
                className="w-full border-sidebar-border text-sidebar-foreground hover:bg-sidebar-accent"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sair
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:pl-64">
          {/* Top bar */}
          <div className="sticky top-0 z-10 bg-white shadow-md border-b border-gray-200">
            <div className="flex items-center justify-between h-16 px-6">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-primary hover:bg-primary/10"
              >
                <Menu className="h-5 w-5" />
              </Button>
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-semibold text-primary">Painel Administrativo</h1>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-muted-foreground">
                  Bem-vindo, <span className="font-medium text-primary">{user?.name?.split(' ')[0]}</span>
                </span>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="p-6">
            {children}
          </main>
        </div>
      </div>
    </AdminProtectedRoute>
  )
}
