"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DollarSign,
  CalendarDays,
  Building,
  Upload,
  ArrowRight,
  ChevronLeft,
  X,
  Zap,
  Lock,
} from "lucide-react"
import Image from "next/image"

// Validation schemas
const anticipationSchema = z.object({
  valorAluguelLiquido: z.string().min(1, "Valor do aluguel é obrigatório"),
  mesesAntecipacao: z.string().min(1, "Número de meses é obrigatório"),
  imobiliaria: z.string().min(1, "Selecione uma imobiliária"),
  contratoFileName: z.string().min(1, "Upload do contrato é obrigatório"),
  dataConsent: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
})

interface RealEstate {
  id: string
  name: string
}

export default function AnticipationPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [realEstates, setRealEstates] = useState<RealEstate[]>([])
  const [user, setUser] = useState<any>(null)

  const form = useForm<z.infer<typeof anticipationSchema>>({
    resolver: zodResolver(anticipationSchema),
    defaultValues: {
      valorAluguelLiquido: "",
      mesesAntecipacao: "",
      imobiliaria: "",
      contratoFileName: "",
      dataConsent: false,
    },
  })

  // Check authentication and load user data
  useEffect(() => {
    const authData = localStorage.getItem("x-auth-state")
    if (!authData) {
      window.location.href = "/"
      return
    }

    try {
      const { user: userData } = JSON.parse(authData)
      setUser(userData)
    } catch (error) {
      console.error("Error parsing auth data:", error)
      window.location.href = "/"
    }
  }, [])

  // Load real estates
  useEffect(() => {
    const loadRealEstates = async () => {
      try {
        const response = await fetch("/api/v1/real-estates")
        if (response.ok) {
          const data = await response.json()
          setRealEstates(data.realEstates || [])
        }
      } catch (error) {
        console.error("Error loading real estates:", error)
      }
    }

    loadRealEstates()
  }, [])

  const maskCurrency = (value: string) => {
    const numericValue = value.replace(/\D/g, "")
    const formattedValue = (Number(numericValue) / 100).toLocaleString("pt-BR", {
      style: "currency",
      currency: "BRL",
    })
    return formattedValue
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && file.type === "application/pdf") {
      form.setValue("contratoFileName", file.name)
      setError("")
    } else if (file) {
      setError("Por favor, envie um arquivo PDF.")
      form.setValue("contratoFileName", "")
    } else {
      form.setValue("contratoFileName", "")
    }
  }

  const onSubmit = async (data: z.infer<typeof anticipationSchema>) => {
    setLoading(true)
    setError("")
    setSuccess("")

    try {
      const authData = localStorage.getItem("x-auth-state")
      if (!authData) {
        window.location.href = "/"
        return
      }

      const { authToken } = JSON.parse(authData)

      // Create FormData for file upload
      const formData = new FormData()
      const fileInput = document.getElementById("contrato") as HTMLInputElement
      const file = fileInput?.files?.[0]

      if (!file) {
        setError("Por favor, selecione um arquivo PDF.")
        return
      }

      formData.append("contract", file)
      formData.append("rentAmount", data.valorAluguelLiquido.replace(/[^\d]/g, ""))
      formData.append("anticipationMonths", data.mesesAntecipacao)
      formData.append("realEstateId", data.imobiliaria)

      const response = await fetch("/api/v1/rental-advance/create", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
        body: formData,
      })

      const result = await response.json()

      if (response.ok) {
        setSuccess("Solicitação criada com sucesso! Redirecionando...")
        setTimeout(() => {
          window.location.href = `/anticipation/validation/${result.id}`
        }, 1500)
      } else {
        setError(result.message || "Erro ao criar solicitação")
      }
    } catch (error) {
      console.error("Error creating anticipation:", error)
      setError("Erro de conexão. Tente novamente.")
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 px-4 pt-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/dashboard"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Voltar para dashboard"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/dashboard"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Fechar"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="px-4">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">Olá, {user?.name?.split(" ")[0]}!</h1>
          <p className="text-white/90 text-sm">Vamos cadastrar os dados da sua operação de antecipação</p>
        </div>

        <Card className="bg-white shadow-xl rounded-2xl border-0 mb-6">
          <CardContent className="space-y-4 p-6">
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                {error}
              </div>
            )}
            {success && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
                {success}
              </div>
            )}

            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <Controller
                name="valorAluguelLiquido"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2">
                    <label htmlFor="valorAluguelLiquido" className="block text-sm font-medium text-gray-700">
                      Valor do Aluguel Líquido (que você recebe) <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="valorAluguelLiquido"
                        placeholder="R$ 0,00"
                        value={field.value}
                        onChange={(e) => {
                          const maskedValue = maskCurrency(e.target.value)
                          field.onChange(maskedValue)
                        }}
                        className={`pl-10 pr-4 ${fieldState.error ? "border-red-500 focus:border-red-500" : ""}`}
                        aria-invalid={!!fieldState.error}
                      />
                    </div>
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <Controller
                name="mesesAntecipacao"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2">
                    <label htmlFor="mesesAntecipacao" className="block text-sm font-medium text-gray-700">
                      Quantos meses deseja antecipar? <span className="text-gray-500">(máx. 12)</span>
                    </label>
                    <div className="relative">
                      <CalendarDays className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="mesesAntecipacao"
                        type="number"
                        min="1"
                        max="12"
                        placeholder="Ex: 6"
                        value={field.value}
                        onChange={(e) => {
                          const value = e.target.value
                          if (value === "" || (Number(value) >= 1 && Number(value) <= 12)) {
                            field.onChange(value)
                          }
                        }}
                        className={`pl-10 pr-4 ${fieldState.error ? "border-red-500 focus:border-red-500" : ""}`}
                        aria-invalid={!!fieldState.error}
                      />
                    </div>
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <Controller
                name="imobiliaria"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Imobiliária <span className="text-red-500">*</span>
                    </label>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className={fieldState.error ? "border-red-500 focus:border-red-500" : ""}>
                        <SelectValue placeholder="Selecione a Imobiliária" />
                      </SelectTrigger>
                      <SelectContent>
                        {realEstates.map((realEstate) => (
                          <SelectItem key={realEstate.id} value={realEstate.id}>
                            {realEstate.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <Controller
                name="contratoFileName"
                control={form.control}
                render={({ field, fieldState }) => (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      📄 Contrato de Locação (PDF) <span className="text-red-500">*</span>
                    </label>
                    <label
                      htmlFor="contrato"
                      className={`flex flex-col items-center justify-center w-full h-16 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${
                        field.value
                          ? "border-blue-500 bg-blue-50 text-blue-600"
                          : "border-gray-300 bg-gray-50 text-gray-500 hover:border-gray-400"
                      } ${fieldState.error ? "border-red-500" : ""}`}
                    >
                      <Upload className={`w-5 h-5 mb-1 ${field.value ? "text-blue-600" : "text-gray-400"}`} />
                      <span className="text-sm font-medium">{field.value || "Clique para fazer upload"}</span>
                      <span className="text-xs text-gray-400">Apenas PDF - até 20MB</span>
                    </label>
                    <Input id="contrato" type="file" accept=".pdf" onChange={handleFileChange} className="hidden" />
                    {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                  </div>
                )}
              />

              <div className="flex items-start space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <Controller
                  name="dataConsent"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <>
                      <input
                        type="checkbox"
                        id="dataConsent"
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        className="mt-0.5 w-4 h-4"
                      />
                      <div>
                        <label htmlFor="dataConsent" className="text-sm text-gray-700 cursor-pointer">
                          Autorizo o uso dos meus dados para análise da antecipação conforme nossa{" "}
                          <Button variant="link" className="p-0 h-auto text-blue-600 text-sm font-medium underline">
                            Política de Privacidade
                          </Button>
                          .
                        </label>
                        {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                      </div>
                    </>
                  )}
                />
              </div>

              <Button 
                type="submit" 
                className="w-full h-12 text-sm font-semibold bg-gradient-to-r from-[#0B4375] to-blue-700 hover:from-[#0B4375] hover:to-blue-800 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
                disabled={loading}
              >
                {loading ? "PROCESSANDO..." : "SOLICITAR ANTECIPAÇÃO"}
                <Zap className="w-4 h-4" />
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Security Badge */}
        <div className="flex items-center justify-center gap-2 mt-4 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
          <Lock className="w-4 h-4 text-white/80" />
          <span className="text-sm text-white/80">Seus dados estão protegidos com criptografia de ponta</span>
        </div>
      </div>
    </div>
  )
}
