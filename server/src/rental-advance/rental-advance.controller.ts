import {
  Controller,
  Get,
  Post,
  Body,
  Req,
  UseGuards,
  UploadedFile,
  UseInterceptors,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { RentalAdvanceService } from './rental-advance.service';
import { CreateRentalAdvanceDto } from './dto/create-rental-advance.dto';
import { ConfirmExtractedDataDto } from './dto/confirm-extracted-data.dto';
import { ProposalRequestDto } from './dto/proposal-request.dto';
import { FinalConfirmationDto } from './dto/final-confirmation.dto';
import { ReviewDecisionDto } from './dto/review-decision.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';
import { Admin } from '../common/decorators/admin.decorator';
import {
  StandardRateLimit,
  UploadRateLimit,
  SensitiveRateLimit,
} from '../rate-limit/rate-limit.decorator';

@Controller('rental-advance')
@UseGuards(JwtAuthGuard)
@StandardRateLimit()
export class RentalAdvanceController {
  constructor(private readonly rentalAdvanceService: RentalAdvanceService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  async listUserRequests(@Req() req) {
    return this.rentalAdvanceService.listUserRequests(req.user.id);
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async getOperation(@Param('id') id: string, @Req() req) {
    return this.rentalAdvanceService.getOperationById(id, req.user.id);
  }

  @Post('create')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FileInterceptor('contractPdf'))
  @UploadRateLimit()
  async createRequest(
    @Req() req,
    @Body() dto: CreateRentalAdvanceDto,
    @UploadedFile() contractPdf: Express.Multer.File,
  ) {
    return this.rentalAdvanceService.createRequest(req.user, dto, contractPdf);
  }

  @Post('confirm-data')
  @HttpCode(HttpStatus.OK)
  @SensitiveRateLimit()
  async confirmExtractedData(@Req() req, @Body() dto: ConfirmExtractedDataDto) {
    return this.rentalAdvanceService.confirmExtractedData(req.user, dto);
  }

  @Post('request-proposal')
  @HttpCode(HttpStatus.OK)
  @SensitiveRateLimit()
  async requestProposal(@Req() req, @Body() dto: ProposalRequestDto) {
    return this.rentalAdvanceService.requestProposal(req.user, dto);
  }

  @Post('final-confirmation')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('identityDoc'))
  @UploadRateLimit()
  async finalConfirmation(
    @Req() req,
    @Body() dto: FinalConfirmationDto,
    @UploadedFile() identityDoc: Express.Multer.File,
  ) {
    return this.rentalAdvanceService.finalConfirmation(
      req.user,
      dto,
      identityDoc,
    );
  }

  @Get(':id/extracted-data')
  @HttpCode(HttpStatus.OK)
  async getExtractedData(@Param('id') operationId: string, @Req() req) {
    return this.rentalAdvanceService.getExtractedData(req.user, operationId);
  }

  // Endpoints para revisão (admin)
  @Get('admin/pending-reviews')
  @UseGuards(AdminGuard)
  @Admin()
  @HttpCode(HttpStatus.OK)
  async getPendingReviews(
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
  ) {
    return this.rentalAdvanceService.getOperationsForReview(page, limit);
  }

  @Get('admin/operation/:id')
  @UseGuards(AdminGuard)
  @Admin()
  @HttpCode(HttpStatus.OK)
  async getOperationForAdmin(@Param('id') id: string) {
    return this.rentalAdvanceService.getOperationById(id);
  }

  @Post('admin/review-decision')
  @UseGuards(AdminGuard)
  @Admin()
  @HttpCode(HttpStatus.OK)
  @SensitiveRateLimit()
  async makeReviewDecision(@Req() req, @Body() dto: ReviewDecisionDto) {
    return this.rentalAdvanceService.reviewDecision(req.user.id, dto);
  }
}
