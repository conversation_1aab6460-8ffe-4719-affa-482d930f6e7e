import {
  Controller,
  Get,
  Query,
  Param,
  HttpCode,
  HttpStatus,
  UseGuards,
  UseInterceptors,
  ParseIntPipe,
  Req,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminGuard } from '../auth/guards/admin.guard';
import { Admin } from '../common/decorators/admin.decorator';
import { AdminSecurityInterceptor } from '../common/interceptors/admin-security.interceptor';
import {
  StandardRateLimit,
} from '../rate-limit/rate-limit.decorator';

@Controller('admin')
@UseGuards(AdminGuard)
@UseInterceptors(AdminSecurityInterceptor)
@Admin()
@StandardRateLimit()
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  /**
   * Verify admin authentication and get admin user info
   */
  @Get('auth/verify')
  @HttpCode(HttpStatus.OK)
  async verifyAdminAuth(@Req() req) {
    return this.adminService.verifyAdminAuth(req.user);
  }

  /**
   * Get dashboard statistics
   */
  @Get('statistics')
  @HttpCode(HttpStatus.OK)
  async getDashboardStatistics() {
    return this.adminService.getDashboardStatistics();
  }

  /**
   * Search users by CPF
   */
  @Get('users/search')
  @HttpCode(HttpStatus.OK)
  async searchUsers(
    @Query('cpf') cpf?: string,
    @Query('name') name?: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
  ) {
    return this.adminService.searchUsers({ cpf, name }, page, limit);
  }

  /**
   * Get all requests for a specific user
   */
  @Get('users/:userId/requests')
  @HttpCode(HttpStatus.OK)
  async getUserRequests(
    @Param('userId') userId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
  ) {
    return this.adminService.getUserRequests(userId, page, limit);
  }

  /**
   * Get enhanced request listing with filters
   */
  @Get('requests')
  @HttpCode(HttpStatus.OK)
  async getRequests(
    @Query('status') status?: string,
    @Query('realEstateId') realEstateId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
  ) {
    return this.adminService.getRequests({
      status,
      realEstateId,
      startDate,
      endDate,
    }, page, limit);
  }

  /**
   * Get detailed user information
   */
  @Get('users/:userId')
  @HttpCode(HttpStatus.OK)
  async getUserDetails(@Param('userId') userId: string) {
    return this.adminService.getUserDetails(userId);
  }

  /**
   * Get real estate companies for filtering
   */
  @Get('real-estates')
  @HttpCode(HttpStatus.OK)
  async getRealEstates() {
    return this.adminService.getRealEstates();
  }

  /**
   * Export requests data (CSV format)
   */
  @Get('requests/export')
  @HttpCode(HttpStatus.OK)
  async exportRequests(
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.adminService.exportRequests({ status, startDate, endDate });
  }
}
