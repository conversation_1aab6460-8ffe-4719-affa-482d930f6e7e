import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { RentalAdvanceModule } from './rental-advance/rental-advance.module';
import { RealEstateModule } from './real-estate/real-estate.module';
import { AdminModule } from './admin/admin.module';
import { ErrorInterceptor } from './common/interceptors/error.interceptor';
import { CacheModule } from './cache/cache.module';
import { RateLimitModule } from './rate-limit/rate-limit.module';

@Module({
  imports: [
    // Cache com Redis
    CacheModule,
    // Rate Limiting
    RateLimitModule,
    // Módulos de negócio
    AuthModule,
    RentalAdvanceModule,
    RealEstateModule,
    AdminModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorInterceptor,
    },
  ],
})
export class AppModule {}
